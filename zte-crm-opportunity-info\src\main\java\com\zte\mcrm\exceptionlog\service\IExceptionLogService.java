package com.zte.mcrm.exceptionlog.service;


import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO;

/**
 * 异常日志记录
 * <AUTHOR>
 *
 */
public interface IExceptionLogService {

    /**
     * 异常日志保存
     * @param logVO
     */
	void saveExceptionLog(ExceptionLogVO logVO);

    /**
     * 异常日志查询
     * @param formData
     * @return
     */
    PageRows<ExceptionLogVO> queryExceptionLog(FormData<ExceptionLogVO> formData);
	
}
