package com.zte.mcrm.exceptionlog.access.vo;

import com.zte.springbootframe.util.string.StringHelper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/****
 * 接口日志记录
 * @ClassName SysInterfaceLogVO
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/2/9 
 * @Version V1.0
 **/
@Data
public class SysInterfaceLogVO {

    /** 主键UUID32位*/
    @ApiModelProperty(value = "主键UUID32位")
    private String id;

    /** 关键字段*/
    @ApiModelProperty(value = "关键字段")
    private String keyField;

    /** 调用位置*/
    @ApiModelProperty(value = "调用位置")
    private String callLocale;

    /** 服务名*/
    @ApiModelProperty(value = "服务名")
    private String serviceName;

    /** 接口名称*/
    @ApiModelProperty(value = "接口名称")
    private String apiName;

    /** 接口参数*/
    @ApiModelProperty(value = "接口参数")
    private String apiParam;

    /** 接口返回结果*/
    @ApiModelProperty(value = "接口返回结果")
    private String apiResult;

    /** 备注说明*/
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /** 法人Id*/
    @ApiModelProperty(value = "法人Id")
    private String legId;

    /** 创建人*/
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /** 创建日期*/
    @ApiModelProperty(value = "创建日期")
    private Date createdDate;

    /** 最后更新人*/
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;

    /** 最后更新日期*/
    @ApiModelProperty(value = "最后更新日期")
    private Date lastUpdatedDate;

    /** 有效标识(Y有效 N无效)*/
    @ApiModelProperty(value = "有效标识(Y有效 N无效)")
    private String enabledFlag;

    public SysInterfaceLogVO(){}

    public SysInterfaceLogVO(String keyField, String callLocale, String apiName, String apiParam, String apiResult){
       this.id = StringHelper.getGUID();
       this.keyField = keyField;
       this.callLocale = callLocale;
       this.apiName = apiName;
       this.apiParam = apiParam;
       this.apiResult = apiResult;
    }

}
