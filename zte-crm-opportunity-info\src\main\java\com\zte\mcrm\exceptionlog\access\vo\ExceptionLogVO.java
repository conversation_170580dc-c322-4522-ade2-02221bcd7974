package com.zte.mcrm.exceptionlog.access.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * 异常实体类
 * <AUTHOR>
 *
 */
@Data
public class ExceptionLogVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**异常ID**/
	private String exceptionId;
	/**访问工号**/
	private String empNo;
	/**访问方法**/
	private String requestMethod;
	/**访问服务**/
	private String syslogtag;
	/**用户终端浏览器信息**/
	private String httpUserAgent;
	/**请求地址，即浏览器中你输入的地址**/
	private String httpHost;
	/**请求的URI 或者 业务操作，可以使用中文等内容，例如：/用户管理/用户登录 必填**/
	private String url;

	/**
	 * 起始时间
	 */
	private String minDate;
	/**
	 * 结束时间
	 */
	private String maxDate;

	/****/
	private String timeLocal;
	private String exceptionContext;
	private String requestParam;
	private String zteReverse1;
	private String zteReverse2;
	private String zteReverse3;

}
