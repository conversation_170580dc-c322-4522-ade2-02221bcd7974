package com.zte.mcrm.exceptionlog.access.dao;

import com.zte.mcrm.exceptionlog.access.vo.SysInterfaceLogVO;
import org.springframework.stereotype.Repository;

/****
 * 接口日志记录
 * <AUTHOR> @date 2021/2/9
 **/
@Repository
public interface SysInterfaceLogDao {

    /****
     * 接口日志保存
     * @methodName saveSysInterfaceLog
     * @param sysInterfaceLog
     * @return int
     * <AUTHOR>
     * @date 2021/2/9
    **/
    int saveSysInterfaceLog(SysInterfaceLogVO sysInterfaceLog);
}
