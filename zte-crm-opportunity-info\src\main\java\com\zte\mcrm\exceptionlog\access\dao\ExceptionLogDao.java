package com.zte.mcrm.exceptionlog.access.dao;

import org.springframework.stereotype.Repository;

import com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO;

import java.util.List;
import java.util.Map;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/
@Repository
public interface ExceptionLogDao {
    /**
     * 异常日志保存
     * @param logVO
     */
	void saveExceptionLog(ExceptionLogVO logVO);

    /**
     * 查询异常日志
     * @param map
     * @return
     */
    public List<ExceptionLogVO> queryExceptionLog(Map<String,Object> map);

    /**
     * 统计异常日志
     * @param map
     * @return
     */
    public int countExceptionLog(Map<String,Object> map);
}
