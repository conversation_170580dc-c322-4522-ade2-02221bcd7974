<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.exceptionlog.access.dao.ExceptionLogDao">
	<!-- 保存异常日志 -->
	<insert id="saveExceptionLog" useGeneratedKeys="true" keyProperty="exceptionId" parameterType="com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO" > 
		INSERT INTO SYS_EXCEPTION
		(
		   EMPNO,
		   REQUEST_METHOD,
		   SYSLOGTAG,
		   HTTP_USER_AGENT,
		   HTTP_HOST,		   
		   URL,
		   TIME_LOCAL,
		   EXCEPTION_CONTEXT,
		   REQUEST_PARAM,
		   CREATION_DATE,
		   ZTEREVERSE1,
		   ZTEREVERSE2,		   
		   ZTEREVERSE3	   
		)
		VALUES
		(
		  #{empNo,jdbcType=VARCHAR},
		  #{requestMethod,jdbcType=VARCHAR},
		  #{syslogtag,jdbcType=VARCHAR},
		  #{httpUserAgent,jdbcType=VARCHAR},
		  #{httpHost,jdbcType=VARCHAR},
		  #{url,jdbcType=VARCHAR},
		  #{timeLocal,jdbcType=VARCHAR},
		  #{exceptionContext,jdbcType=VARCHAR},
		  #{requestParam,jdbcType=VARCHAR},
		  NOW(),
		  #{zteReverse1,jdbcType=VARCHAR},
		  #{zteReverse2,jdbcType=VARCHAR},	
		  #{zteReverse3,jdbcType=VARCHAR}
		)
	</insert>
	<!-- 获取日志信息 -->
	<select id="queryExceptionLog" parameterType="map" resultType="com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO">
		select
		EMPNO empNo,
		REQUEST_METHOD requestMethod,
		SYSLOGTAG syslogtag,
		HTTP_USER_AGENT httpUserAgent,
		HTTP_HOST httpHost,
		URL url,
		TIME_LOCAL timeLocal,
		EXCEPTION_CONTEXT as exceptionContext,
		REQUEST_PARAM requestParam,
		ZTEREVERSE1 zteReverse1,
		ZTEREVERSE2,
		ZTEREVERSE3
		from SYS_EXCEPTION S
		WHERE 1=1
		<if test="empNo != '' and null != empNo">
			AND S.EMPNO=#{empNo,jdbcType=VARCHAR}
		</if>
		<if test="serviceName != null and serviceName != ''">
			AND S.SYSLOGTAG = #{serviceName,jdbcType=VARCHAR}
		</if>
		<if test="exceptionContext != null and exceptionContext != ''">
			AND S.EXCEPTION_CONTEXT like concat(concat('%',#{exceptionContext,jdbcType=VARCHAR}),'%')
		</if>
		<if test="requestParam != null and requestParam != ''">
			AND S.REQUEST_PARAM like concat(concat('%',#{requestParam,jdbcType=VARCHAR}),'%')
		</if>
		<if test="requestMethod != null and requestMethod != ''">
			AND S.REQUEST_METHOD = #{requestMethod,jdbcType=VARCHAR}
		</if>
		<if test="url != null and url != ''">
			AND S.URL like concat(concat('%',#{url,jdbcType=VARCHAR}),'%')
		</if>
		<if test="zteReverse1 != null and zteReverse1 != ''">
			AND S.ZTEREVERSE1 like concat(concat('%',#{zteReverse1,jdbcType=VARCHAR}),'%')
		</if>
		<if test="minDate != null and  minDate != ''">
			<![CDATA[ and  date_format(S.TIME_LOCAL,'%Y-%m-%d %H:%i:%S') >= date_format(#{minDate,jdbcType=TIMESTAMP},'%Y-%m-%d %H:%i:%S')]]>
		</if>
		<if test="maxDate != null and  maxDate != ''">
			<![CDATA[ and  date_format(S.TIME_LOCAL,'%Y-%m-%d %H:%i:%S') <= date_format(#{maxDate,jdbcType=TIMESTAMP},'%Y-%m-%d %H:%i:%S')]]>
		</if>
		ORDER BY S.TIME_LOCAL DESC
		LIMIT #{startRow},#{rowSize}
	</select>
	<!-- 统计日志信息 -->
	<select id="countExceptionLog" parameterType="map" resultType="int">
		select count(1)
		from SYS_EXCEPTION S
		WHERE 1=1
		<if test="empNo != '' and null != empNo">
			AND S.EMPNO=#{empNo,jdbcType=VARCHAR}
		</if>
		<if test="serviceName != null and serviceName != ''">
			AND S.SYSLOGTAG = #{serviceName,jdbcType=VARCHAR}
		</if>
		<if test="exceptionContext != null and exceptionContext != ''">
			AND S.EXCEPTION_CONTEXT like concat(concat('%',#{exceptionContext,jdbcType=VARCHAR}),'%')
		</if>
		<if test="requestParam != null and requestParam != ''">
			AND S.REQUEST_PARAM like concat(concat('%',#{requestParam,jdbcType=VARCHAR}),'%')
		</if>
		<if test="requestMethod != null and requestMethod != ''">
			AND S.REQUEST_METHOD = #{requestMethod,jdbcType=VARCHAR}
		</if>
		<if test="url != null and url != ''">
			AND S.URL like concat(concat('%',#{url,jdbcType=VARCHAR}),'%')
		</if>
		<if test="zteReverse1 != null and zteReverse1 != ''">
			AND S.ZTEREVERSE1 like concat(concat('%',#{zteReverse1,jdbcType=VARCHAR}),'%')
		</if>
		<if test="minDate != null and  minDate != ''">
			<![CDATA[ and  date_format(S.TIME_LOCAL,'%Y-%m-%d %H:%i:%S') >= date_format(#{minDate,jdbcType=TIMESTAMP},'%Y-%m-%d %H:%i:%S')]]>
		</if>
		<if test="maxDate != null and  maxDate != ''">
			<![CDATA[ and  date_format(S.TIME_LOCAL,'%Y-%m-%d %H:%i:%S') <= date_format(#{maxDate,jdbcType=TIMESTAMP},'%Y-%m-%d %H:%i:%S')]]>
		</if>
	</select>
	
</mapper>
