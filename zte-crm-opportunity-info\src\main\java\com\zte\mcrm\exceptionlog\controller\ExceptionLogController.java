package com.zte.mcrm.exceptionlog.controller;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO;
import com.zte.mcrm.exceptionlog.service.IExceptionLogService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 * @ClassName:ExceptionLogController 
 * @Description: 
 * @author: 6092002949
 * @date: 2019年4月19日
 */
@RestController
@Api("SmartSales公共-异常日志")
@RequestMapping("/exceptionlog")
public class ExceptionLogController {

	/** 异常日志服务 **/
	@Autowired
	IExceptionLogService exceptionLogService;

	/**
	 * 获取异常日志数据
	 * @return
	 */
	@ApiOperation("异常日志查询")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType = "String", required = true, value = "租户ID", defaultValue = "smartsales"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType = "String", required = true, value = "语言", defaultValue = "zh_CH"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType = "String", required = true, value = "工号", defaultValue = "00181547")
	})
	@RequestMapping(value = "/queryExceptionLog", method = RequestMethod.POST)
	public ServiceData<PageRows<ExceptionLogVO>> query(
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID) String tenantId,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID) String lang,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String xEmpNo,
			@RequestBody FormData<ExceptionLogVO> formData) throws Exception {
        return ServiceResultUtil.success(exceptionLogService.queryExceptionLog(formData));
	}
	
}
