

## 4.3 核心表设计

### 4.3.1 任务表 (soc_task)

**表结构设计**:
```sql
CREATE TABLE soc_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码，格式：TASK001',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS,文档库,项目文档,历史SOC文档',
    attachment_file_path VARCHAR(500) COMMENT '应答条目文件路径',
    task_status VARCHAR(20) DEFAULT '未开始' COMMENT '任务状态：未开始-NOT_STARTED,进行中-IN_PROGRESS,已完成-COMPLETED',
    is_personal TINYINT(1) DEFAULT 0 COMMENT '是否为个人任务：0-否,1-是',
    create_emp VARCHAR(100) NOT NULL COMMENT '创建人（姓名+工号）',
    create_emp_no VARCHAR(50) NOT NULL COMMENT '创建人工号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_task_code (task_code),
    INDEX idx_create_emp_no (create_emp_no),
    INDEX idx_task_status (task_status),
    INDEX idx_create_time (create_time),
    INDEX idx_country_mto (country, mto_branch),
    INDEX idx_customer_project (customer, project),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_composite_query (create_emp_no, task_status, is_deleted, create_time)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答任务表';
```



### 4.3.2 条目表 (soc_item)

**表结构设计**:
```sql
CREATE TABLE soc_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    additional_info TEXT COMMENT '补充信息',
    assign_emp VARCHAR(100) COMMENT '指派给（姓名+工号）',
    assign_emp_no VARCHAR(50) COMMENT '指派给用户ID-empNo',
    item_status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态：NOT_ANSWERED-未应答,ANSWERING-应答中,ANSWERED-已应答',
    auto_response TINYINT(1) DEFAULT 1 COMMENT '是否自动应答：0-否,1-是',
    overwrite_when_duplicate TINYINT(1) DEFAULT 1 COMMENT '重复时是否覆盖：0-否,1-是',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_task_id (task_id),
    INDEX idx_item_code (item_code),
    INDEX idx_assign_emp_no (assign_emp_no),
    INDEX idx_item_status (item_status),
    INDEX idx_create_time (create_time),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_composite_task_item (task_id, item_code, is_deleted),
    INDEX idx_composite_assign (assign_emp_no, item_status, is_deleted),
    FULLTEXT INDEX idx_description_fulltext (item_description),
    UNIQUE KEY uk_task_item_code (task_id, item_code, is_deleted)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答条目表';
```

### 4.3.3 条目产品关联表 (soc_item_product)

**表结构设计**:
```sql
CREATE TABLE soc_item_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    product_code VARCHAR(200) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI-AI应答,MANUAL-手工应答',
    response_content LONGTEXT COMMENT '应答说明（支持富文本）',
    source VARCHAR(50) COMMENT '应答来源：GBBS,文档库等',
    source_index VARCHAR(200) COMMENT '索引链接',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    item_product_status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态：NOT_ANSWERED-未应答,ANSWERING-应答中,ANSWERED-已应答',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_id (item_id),
    INDEX idx_task_id (task_id),
    INDEX idx_product_code (product_code),
    INDEX idx_satisfaction (satisfaction),
    INDEX idx_item_product_status (item_product_status),
    INDEX idx_match_score (match_score DESC),
    INDEX idx_create_time (create_time),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_composite_item_product (item_id, product_code, is_deleted),
    INDEX idx_composite_task_product (task_id, product_code, satisfaction),
    INDEX idx_composite_status_score (item_product_status, match_score DESC, is_deleted)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目产品关联表';
```


## 4.4 扩展表设计

### 4.4.1 标签相关表

**标签字典表 (soc_tag)**:
```sql
CREATE TABLE soc_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    create_emp_no VARCHAR(50) NOT NULL COMMENT '创建人工号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    UNIQUE KEY uk_tag_name (tag_name),
    INDEX idx_create_emp_no (create_emp_no),
    INDEX idx_usage_count (usage_count DESC),
    INDEX idx_is_active (is_active),
    INDEX idx_create_time (create_time)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签字典表';
```

**条目标签关联表 (soc_item_tag)**:
```sql
CREATE TABLE soc_item_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称（冗余字段）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_id (item_id),
    INDEX idx_tag_id (tag_id),
    INDEX idx_tag_name (tag_name),
    UNIQUE KEY uk_item_tag (item_id, tag_id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目标签关联表';
```

### 4.4.2 AI匹配结果表

**AI匹配结果表 (soc_ai_match_result)**:
```sql
CREATE TABLE soc_ai_match_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配结果ID',
    item_product_id BIGINT NOT NULL COMMENT '条目产品关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源：GBBS,文档库等',
    source_id VARCHAR(100) COMMENT '数据源中的记录ID',
    source_description TEXT COMMENT '数据源条目描述',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度分数',
    semantic_score DECIMAL(5,2) COMMENT '语义相似度分数',
    context_score DECIMAL(5,2) COMMENT '上下文匹配分数',
    country_match TINYINT(1) DEFAULT 0 COMMENT '国家匹配：0-否,1-是',
    branch_match TINYINT(1) DEFAULT 0 COMMENT '分支匹配：0-否,1-是',
    customer_match TINYINT(1) DEFAULT 0 COMMENT '客户匹配：0-否,1-是',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_content LONGTEXT COMMENT '应答说明',
    source_index VARCHAR(200) COMMENT '索引链接',
    is_applied TINYINT(1) DEFAULT 0 COMMENT '是否已应用：0-否,1-是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_product_id (item_product_id),
    INDEX idx_item_id (item_id),
    INDEX idx_task_id (task_id),
    INDEX idx_data_source (data_source),
    INDEX idx_match_score (match_score DESC),
    INDEX idx_semantic_score (semantic_score DESC),
    INDEX idx_satisfaction (satisfaction),
    INDEX idx_is_applied (is_applied),
    INDEX idx_create_time (create_time),
    INDEX idx_composite_match (item_id, match_score DESC, is_applied),
    INDEX idx_composite_source (data_source, source_id),
    INDEX idx_composite_score (match_score DESC, semantic_score DESC, context_score DESC)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配结果表';
```

### 4.4.3 历史版本表

**历史版本表 (soc_item_history)**:
```sql
CREATE TABLE soc_item_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史版本ID',
    item_product_id BIGINT NOT NULL COMMENT '条目产品关联ID',
    version_num INT NOT NULL COMMENT '版本号',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI-AI应答,MANUAL-手工应答',
    response_content LONGTEXT COMMENT '应答说明',
    source VARCHAR(50) COMMENT '应答来源',
    source_index VARCHAR(200) COMMENT '索引链接',
    additional_info TEXT COMMENT '补充信息',
    remark TEXT COMMENT '备注',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型：create,update,ai_enhance,manual_edit',
    change_description VARCHAR(500) COMMENT '变更说明',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_emp_no VARCHAR(50) COMMENT '操作人工号',

    -- 审计字段
    created_by VARCHAR(30) NOT NULL COMMENT '创建人',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(30) NOT NULL COMMENT '最后更新人',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_product_id (item_product_id),
    INDEX idx_version_num (version_num),
    INDEX idx_change_type (change_type),
    INDEX idx_create_time (create_time),
    INDEX idx_create_emp_no (create_emp_no),
    INDEX idx_composite_version (item_product_id, version_num DESC),
    INDEX idx_composite_change (item_product_id, change_type, create_time DESC)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目历史版本表';
```

## 4.5 数据库ER图

### 4.5.1 核心表关系图

以下是SOC应答系统的核心表关系图，展示了各表之间的关联关系：

```mermaid
erDiagram
    soc_task ||--o{ soc_item : "一对多"
    soc_task ||--o{ soc_item_product : "一对多"
    soc_item ||--o{ soc_item_product : "一对多"
    soc_item ||--o{ soc_item_tag : "一对多"
    soc_tag ||--o{ soc_item_tag : "一对多"
    soc_item_product ||--o{ soc_ai_match_result : "一对多"
    soc_item_product ||--o{ soc_item_history : "一对多"

    soc_task {
        BIGINT id PK "任务ID"
        VARCHAR task_code UK "任务编码"
        VARCHAR task_name "任务名称"
        VARCHAR country "国家/MTO"
        VARCHAR mto_branch "MTO分支"
        VARCHAR customer "客户"
        VARCHAR project "项目"
        VARCHAR data_source "数据源"
        VARCHAR attachment_file_path "应答条目文件路径"
        VARCHAR task_status "任务状态"
        TINYINT is_personal "是否为个人任务"
        VARCHAR create_emp "创建人"
        VARCHAR create_emp_no "创建人工号"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
        TINYINT is_deleted "是否删除"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_item {
        BIGINT id PK "条目ID"
        BIGINT task_id FK "任务ID"
        VARCHAR item_code "条目编号"
        TEXT item_description "条目描述"
        TEXT additional_info "补充信息"
        VARCHAR assign_emp "指派给"
        VARCHAR assign_emp_no "指派给用户ID"
        VARCHAR item_status "应答状态"
        TINYINT auto_response "是否自动应答"
        TINYINT overwrite_when_duplicate "重复时是否覆盖"
        TEXT remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
        TINYINT is_deleted "是否删除"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_item_product {
        BIGINT id PK "关联ID"
        BIGINT item_id FK "条目ID"
        BIGINT task_id FK "任务ID"
        VARCHAR product_code "产品编码"
        VARCHAR product_name "产品名称"
        VARCHAR satisfaction "满足度"
        VARCHAR response_method "应答方式"
        LONGTEXT response_content "应答说明"
        VARCHAR source "应答来源"
        VARCHAR source_index "索引链接"
        DECIMAL match_score "匹配度分数"
        VARCHAR item_product_status "应答状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
        TINYINT is_deleted "是否删除"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_tag {
        BIGINT id PK "标签ID"
        VARCHAR tag_name UK "标签名称"
        VARCHAR tag_color "标签颜色"
        INT usage_count "使用次数"
        VARCHAR create_emp_no "创建人工号"
        DATETIME create_time "创建时间"
        TINYINT is_active "是否激活"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_item_tag {
        BIGINT id PK "关联ID"
        BIGINT item_id FK "条目ID"
        BIGINT tag_id FK "标签ID"
        VARCHAR tag_name "标签名称"
        DATETIME create_time "创建时间"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_ai_match_result {
        BIGINT id PK "匹配结果ID"
        BIGINT item_product_id FK "条目产品关联ID"
        BIGINT item_id FK "条目ID"
        BIGINT task_id FK "任务ID"
        VARCHAR data_source "数据源"
        VARCHAR source_id "数据源记录ID"
        TEXT source_description "数据源条目描述"
        DECIMAL match_score "匹配度分数"
        DECIMAL semantic_score "语义相似度分数"
        DECIMAL context_score "上下文匹配分数"
        TINYINT country_match "国家匹配"
        TINYINT branch_match "分支匹配"
        TINYINT customer_match "客户匹配"
        VARCHAR satisfaction "满足度"
        LONGTEXT response_content "应答说明"
        VARCHAR source_index "索引链接"
        TINYINT is_applied "是否已应用"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }

    soc_item_history {
        BIGINT id PK "历史版本ID"
        BIGINT item_product_id FK "条目产品关联ID"
        INT version_num "版本号"
        VARCHAR satisfaction "满足度"
        VARCHAR response_method "应答方式"
        LONGTEXT response_content "应答说明"
        VARCHAR source "应答来源"
        VARCHAR source_index "索引链接"
        TEXT additional_info "补充信息"
        TEXT remark "备注"
        VARCHAR change_type "变更类型"
        VARCHAR change_description "变更说明"
        DATETIME create_time "创建时间"
        VARCHAR create_emp_no "操作人工号"
        VARCHAR created_by "创建人"
        DATETIME created_date "创建时间"
        VARCHAR last_updated_by "最后更新人"
        DATETIME last_updated_date "最后更新时间"
        CHAR enabled_flag "有效标记"
        BIGINT tenant_id "租户ID"
    }
```

### 4.5.2 表关系说明

**主要关系链路**：
1. **任务 → 条目 → 条目产品关联**：一个任务包含多个条目，每个条目可以关联多个产品
2. **条目 → 标签关联**：条目可以打多个标签，支持分类管理
3. **条目产品关联 → AI匹配结果**：每个条目产品关联可以有多个AI匹配结果
4. **条目产品关联 → 历史版本**：记录条目产品关联的变更历史

**关键外键约束**：
- `soc_item.task_id` → `soc_task.id`
- `soc_item_product.item_id` → `soc_item.id`
- `soc_item_product.task_id` → `soc_task.id`
- `soc_item_tag.item_id` → `soc_item.id`
- `soc_item_tag.tag_id` → `soc_tag.id`
- `soc_ai_match_result.item_product_id` → `soc_item_product.id`
- `soc_ai_match_result.item_id` → `soc_item.id`
- `soc_ai_match_result.task_id` → `soc_task.id`
- `soc_item_history.item_product_id` → `soc_item_product.id`

### 4.5.3 审计字段说明

所有表都包含以下标准审计字段：
- `created_by`：创建人，记录数据创建者
- `created_date`：创建时间，自动设置为当前时间
- `last_updated_by`：最后更新人，记录数据最后修改者
- `last_updated_date`：最后更新时间，自动更新为当前时间
- `enabled_flag`：有效标记，Y表示有效，N表示无效
- `tenant_id`：租户ID，支持多租户数据隔离，默认值为10001
